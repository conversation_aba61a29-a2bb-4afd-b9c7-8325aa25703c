using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.IO;
using System.Threading.Tasks;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Commands.DQA.Desk_Level;
using WHO.MALARIA.Domain.Commands.DQA.Elimination;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Dtos.OutputDtos.DeskLevelDQA;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Services.Handlers.Queries;

namespace WHO.MALARIA.Web.Apis
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class DQAController : BaseApiController
    {
        private readonly IDQAQueries _dqaQueries;
        private readonly ILogger<DQAController> _logger;
        private string language;

        public DQAController(IMediator mediator, IHttpContextAccessor httpContextAccessor, IDQAQueries dqaQueries, ILogger<DQAController> logger) : base(mediator, httpContextAccessor)
        {
            _dqaQueries = dqaQueries;
            _logger = logger;
            language = httpContextAccessor.HttpContext?.Request?.Cookies[Constants.Common.I18next] ?? Constants.Common.DefaultLanguage;
        }

        #region Desk Level

        /// <summary>
        /// Get desk level variable list for the given assessment.
        /// </summary>
        /// <param name="assessmentId">Assessment Id to fetch details of desk level report</param>
        /// <returns>List of instance of DQAReportDto</returns>
        [HttpGet]
        [Route("deskLevel/selectedParameters/{assessmentId}")]
        public async Task<ActionResult> GetDeskLevelSelectedParametersAsync(Guid assessmentId)
        {
            SelectedParametersDto parameters = await _dqaQueries.GetDeskLevelSelectedParametersAsync(assessmentId);

            return Ok(parameters);
        }

        /// <summary>
        /// Save desk level parameters
        /// </summary>
        /// <param name="command">Instance of SaveDQADeskLevelCommand class - Contains input parameters</param>
        /// <returns>200 HttpStatus code if request is successfully completed Else 500 Internal server error OR 400 Bad request</returns>
        [HttpPost]
        [Route("deskLevel/save")]
        public async Task<ActionResult> SaveDeskLevel(SaveDQADeskLevelCommand command)
        {
            command.CurrentUserId = GetCurrentUser().UserId;

            return Ok(await CommandAsync(command));
        }

        /// <summary>
        /// Finalize desk level parameters
        /// </summary>
        /// <param name="command">Instance of FinalizeNationalLevelResultCommand class - Contains input parameters</param>
        /// <returns>return true if data finalized successfully</returns>
        [HttpPost]
        [Route("deskLevel/finalize")]
        public async Task<ActionResult> FinalizeDeskLevel(FinalizeDQADeskLevelCommand command)
        {
            command.CurrentUserId = GetCurrentUser().UserId;

            return Ok(await CommandAsync(command));
        }

        /// <summary>
        ///  Get desk Level DQA template
        /// </summary>
        /// <param name="assessmentId">Assessment Id to fetch details of desk level report</param>
        /// <returns>return DQA Desk level template file</returns>
        [HttpGet]
        [Route("deskLevel/generateTemplate/{assessmentId}")]
        public async Task<FileContentResult> GenerateDeskLevelTemplate(Guid assessmentId)
        {
            try
            {
                FileResponseDto fileResponseDto = await _dqaQueries.GetDeskLevelTemplate(assessmentId);

                if (fileResponseDto?.FileData == null || fileResponseDto.FileData.Length == 0)
                {
                    throw new InvalidOperationException("Failed to generate desk level template - no file data returned");
                }

                FileContentResult result = new FileContentResult(fileResponseDto.FileData, Constants.DownloadDocument.ExcelFormat)
                {
                    FileDownloadName = fileResponseDto.FileName
                };

                return result;
            }
            catch (Exception ex)
            {
                // Log the error for debugging
                Console.WriteLine($"Error generating desk level template for assessment {assessmentId}: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                throw;
            }
        }

        /// <summary>
        /// Upload an excel file of desk level DQA
        /// </summary>
        /// <param name="command">Instance of ProcessDeskLevelFileCommand</param>
        /// <returns>return true if data insertd successfully</returns>
        [HttpPost, DisableRequestSizeLimit]
        [Route("deskLevel/processFile")]
        public async Task<ActionResult> ProcessDeskLevelFile([FromForm] ProcessDeskLevelFileCommand command)
        {
            try
            {
                command.CurrentUserId = base.GetCurrentUser().UserId;
                // do not get cookies value in constructor
<<<<<<< HEAD
                command.LanguageId = HttpContext?.Request?.Cookies[Constants.Common.I18next] ?? Constants.Common.DefaultLanguage;
                bool isSuccess = await CommandAsync(command);

=======
                command.LanguageId =  HttpContext?.Request?.Cookies[Constants.Common.I18next] ?? Constants.Common.DefaultLanguage;

                Console.WriteLine($"Processing desk level file upload for assessment {command.AssessmentId}");
                bool isSuccess = await CommandAsync(command);

>>>>>>> bug/116061-115838
                return Ok(isSuccess);
            }
            catch (Exception ex)
            {
<<<<<<< HEAD
                _logger.LogError(ex, $"Error processing desk level file for assessment {command.AssessmentId} by user {base.GetCurrentUser().UserId}: {ex.Message}");
=======
                // Log the error for debugging
                Console.WriteLine($"Error processing desk level file upload for assessment {command?.AssessmentId}: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
>>>>>>> bug/116061-115838
                throw;
            }
        }

        /// <summary>
        /// Generates an excel report file of desk level DQA  
        /// </summary>
        /// <param name="assessmentId">Assessment Id to fetch details of desk level report</param>
        /// <returns>return DQA Desk level report file</returns>
        [HttpGet]
        [Route("desklevel/report/export/{assessmentId}")]
        public async Task<FileContentResult> GenerateDeskLevelReportFileAsync(Guid assessmentId)
        {
            FileResponseDto fileResponseDto = await _dqaQueries.GetDeskLevelReport(assessmentId);

            FileContentResult result = new FileContentResult(fileResponseDto.FileData, Constants.DownloadDocument.ExcelFormat)
            {
                FileDownloadName = fileResponseDto.FileName
            };

            return result;
        }

        /// <summary>
        /// Get desk level report for the given assessment.
        /// </summary>
        /// <param name="indicatorReportType">indicator report type</param>
        /// <param name="assessmentId">Assessment Id to fetch details of desk level report</param>
        /// <returns>List of instance of DQAReportDto</returns>
        [HttpGet]
        [Route("deskLevel/report/{indicatorReportType}/{assessmentId}")]
        public async Task<ActionResult> GetDeskLevelReportAsync(DQAIndicatorReport indicatorReportType, Guid assessmentId)
        {
            return Ok(await _dqaQueries.GetReportForIndicatorAsync(indicatorReportType, assessmentId));
        }

        /// <summary>
        /// Get desk level indicator list.
        /// </summary>
        /// <returns>List of instance of IndicatorListDto</returns>
        [HttpGet]
        [Route("deskLevel/indicators")]
        public async Task<ActionResult> GetDeskLevelIndicatorsAsync()
        {
            return Ok(await _dqaQueries.GetDeskLevelIndicatorsAsync());
        }

        /// <summary>
        /// Get desk level national level summary data years for the assessment
        /// </summary>
        /// <param name="assessmentId">Id of an assessment</param>
        /// <returns>List of years</returns>-
        [HttpGet]
        [Route("deskLevel/summary/years/{assessmentId}")]
        public async Task<ActionResult> GetDeskLevelNationalSummaryYearsAsync(Guid assessmentId)
        {
            return Ok(await _dqaQueries.GetDeskLevelNationalSummaryYearsAsync(assessmentId));
        }

        /// <summary>
        /// Get yearwise National Level Result for desk level for an assessment.
        /// </summary>
        /// <returns>List of Instance of NationalLevelSummaryRequestDto</returns>
        [HttpGet]
        [Route("deskLevel/nationalLevelSummary/{assessmentId}/{year}")]
        public async Task<ActionResult> GetDeskLevelNationalLevelSummaryDataAsync(Guid assessmentId, short year)
        {
            return Ok(await _dqaQueries.GetDeskLevelNationalSummaryDataAsync(assessmentId, year));
        }

        /// <summary>
        /// Save yearwise National Level Target for desk level for an assessment.
        /// </summary>
        /// <returns>return true if data saved successfully</returns>
        [HttpPost]
        [Route("deskLevel/SaveNationalLevelTarget")]
        public async Task<ActionResult> SaveDeskLevelNationalLevelTargetAsync([FromBody] SaveNationalLevelTargetCommand command)
        {
            command.CurrentUserId = base.GetCurrentUser().UserId;
            bool isSuccess = await CommandAsync(command);

            return Ok(isSuccess);
        }

        /// <summary>
        /// Finalize yearwise National Level Target for desk level for an assessment.
        /// </summary>
        /// <param name="command">Instance of FinalizeNationalLevelResultCommand class - Contains input parameters</param>
        /// <returns>return true if data finalized successfully</returns>
        [HttpPost]
        [Route("deskLevel/FinalizeNationalLevelResult")]
        public async Task<ActionResult> FinalizeDeskLevelNationalLevelTargetAsync([FromBody] Domain.Commands.DQA.Desk_Level.FinalizeNationalLevelResultCommand command)
        {
            command.CurrentUserId = base.GetCurrentUser().UserId;
            bool isSuccess = await CommandAsync(command);

            return Ok(isSuccess);
        }
        #endregion

        #region Elimination

        /// <summary>
        /// Download DQA Elimination template
        /// </summary>
        /// <returns>Instance of FileContentResult</returns>
        [HttpGet, DisableRequestSizeLimit]
        [Route("elimination/download")]
        public IActionResult GetEliminationTemplate()
        {
            string folderPath = "";
            if (language == "fr")
            {
                folderPath = Directory.GetCurrentDirectory() + Constants.DownloadDocument.DQAEliminationTemplateFilePath_FR;
            }
            else
            {
                folderPath = Directory.GetCurrentDirectory() + Constants.DownloadDocument.DQAEliminationTemplateFilePath;
            }

            FileStream fileStream = new FileStream(folderPath, FileMode.Open);

            return File(fileStream, Constants.DownloadDocument.ContentTypeForExcel, Constants.DownloadDocument.DQAEliminationFileName);
        }

        /// <summary>
        /// Save yearwise National Level Target for elimination assessment.
        /// </summary>
        /// <param name="command">Instance of SaveNationalLevelResultCommand class - Contains input parameters</param>
        /// <returns>return true if data saved successfully</returns>
        [HttpPost]
        [Route("elimination/SaveNationalLevelResult")]
        public async Task<ActionResult> SaveEliminationNationalLevelTargetAsync([FromBody] SaveNationalLevelResultCommand command)
        {
            command.CurrentUserId = base.GetCurrentUser().UserId;
            bool isSuccess = await CommandAsync(command);

            return Ok(isSuccess);
        }

        /// <summary>
        /// Get yearwise elimination National Level Result for an assessment.
        /// </summary>
        /// <returns>returns instance of NationalLevelSummaryRequestDto</returns>
        [HttpGet]
        [Route("elimination/nationalLevelSummary/{assessmentId}")]
        public async Task<ActionResult> GetEliminationNationalSummaryDataAsync(Guid assessmentId)
        {
            return Ok(await _dqaQueries.GetEliminationNationalSummaryDataAsync(assessmentId));
        }

        /// <summary>
        /// Generates an excel report file of elimination DQA  
        /// </summary>
        /// <param name="assessmentId">Assessment Id to fetch details of desk level report</param>
        /// <returns>return DQA elimination report file</returns>
        [HttpGet, DisableRequestSizeLimit]
        [Route("elimination/report/export/{assessmentId}")]
        public async Task<FileContentResult> GenerateEliminationReportFileAsync(Guid assessmentId)
        {
            FileResponseDto fileResponseDto = await _dqaQueries.GetEliminationReportAsync(assessmentId);

            FileContentResult result = new FileContentResult(fileResponseDto.FileData, Constants.DownloadDocument.ExcelFormat)
            {
                FileDownloadName = fileResponseDto.FileName
            };

            return result;
        }

        /// <summary>
        /// Finalize yearwise National Level Target for desk level for an assessment.
        /// </summary>
        /// <param name="command">Instance of FinalizeNationalLevelResultCommand class - Contains input parameters</param>
        /// <returns>return true if data finalized successfully</returns>
        [HttpPost]
        [Route("elimination/FinalizeNationalLevelResult")]
        public async Task<ActionResult> FinalizeEliminationNationalLevelTargetAsync([FromBody] Domain.Commands.DQA.Elimination.FinalizeNationalLevelResultCommand command)
        {
            command.CurrentUserId = base.GetCurrentUser().UserId;
            bool isSuccess = await CommandAsync(command);

            return Ok(isSuccess);
        }

        #endregion

        /// <summary>
        /// Diagnostic endpoint to check DQA template files existence
        /// </summary>
        /// <returns>Status of template files</returns>
        [HttpGet]
        [Route("diagnostic/templates")]
        public IActionResult CheckTemplateFiles()
        {
            var diagnostics = new
            {
                CurrentDirectory = Environment.CurrentDirectory,
                Templates = new
                {
                    ServiceLevel_EN = new
                    {
                        Path = Path.Combine(Environment.CurrentDirectory, "Templates", "DQA", "DQA_SERVICE_EN.xlsx"),
                        Exists = System.IO.File.Exists(Path.Combine(Environment.CurrentDirectory, "Templates", "DQA", "DQA_SERVICE_EN.xlsx"))
                    },
                    ServiceLevel_FR = new
                    {
                        Path = Path.Combine(Environment.CurrentDirectory, "Templates", "DQA", "DQA_SERVICE_FR.xlsx"),
                        Exists = System.IO.File.Exists(Path.Combine(Environment.CurrentDirectory, "Templates", "DQA", "DQA_SERVICE_FR.xlsx"))
                    },
                    DeskLevel_Introduction_EN = new
                    {
                        Path = Path.Combine(Environment.CurrentDirectory, "Templates", "DQA", "DeskLevelDQA_introduction_tab_EN.xlsx"),
                        Exists = System.IO.File.Exists(Path.Combine(Environment.CurrentDirectory, "Templates", "DQA", "DeskLevelDQA_introduction_tab_EN.xlsx"))
                    },
                    DeskLevel_Introduction_FR = new
                    {
                        Path = Path.Combine(Environment.CurrentDirectory, "Templates", "DQA", "DeskLevelDQA_introduction_tab_FR.xlsx"),
                        Exists = System.IO.File.Exists(Path.Combine(Environment.CurrentDirectory, "Templates", "DQA", "DeskLevelDQA_introduction_tab_FR.xlsx"))
                    },
                    DeskLevel_Report_EN = new
                    {
                        Path = Path.Combine(Environment.CurrentDirectory, "Templates", "DQA", "DQA_desk_level_tool_report_EN.xlsx"),
                        Exists = System.IO.File.Exists(Path.Combine(Environment.CurrentDirectory, "Templates", "DQA", "DQA_desk_level_tool_report_EN.xlsx"))
                    },
                    DeskLevel_Report_FR = new
                    {
                        Path = Path.Combine(Environment.CurrentDirectory, "Templates", "DQA", "DQA_desk_level_tool_report_FR.xlsx"),
                        Exists = System.IO.File.Exists(Path.Combine(Environment.CurrentDirectory, "Templates", "DQA", "DQA_desk_level_tool_report_FR.xlsx"))
                    },
                    Elimination = new
                    {
                        Path = Path.Combine(Environment.CurrentDirectory, "Templates", "DQA", "DQA_Elimination.xlsx"),
                        Exists = System.IO.File.Exists(Path.Combine(Environment.CurrentDirectory, "Templates", "DQA", "DQA_Elimination.xlsx"))
                    }
                }
            };

            return Ok(diagnostics);
        }

        /// <summary>
        /// Upload an excel file of service level DQA
        /// </summary>
        /// <param name="command">Object of CreateOrUpdateSLDQASummaryCommand</param>
        /// <returns>return true if data insertd successfully</returns>
        [HttpPost, DisableRequestSizeLimit]
        [Route("sl/upload")]
        public async Task<ActionResult> Upload([FromForm] CreateServiceLevelSummaryCommand command)
        {
            try
            {
                command.CurrentUserId = base.GetCurrentUser().UserId;
                command.Language = language;

                Console.WriteLine($"Processing service level file upload for service level {command.ServiceLevelId}");
                bool isSuccess = await CommandAsync(command);

                return Ok(isSuccess);
            }
            catch (Exception ex)
            {
                // Log the error for debugging
                Console.WriteLine($"Error processing service level file upload for service level {command?.ServiceLevelId}: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                throw;
            }
        }

        /// <summary>
        /// Gets DQA variables for particular strategy
        /// </summary>
        /// <param name="strategyId">Strategy id for which DQA variables are to be fetched</param>
        ///<param name="includeSystemDefined">Boolean value if IsSystemDefined variables to be included in result, true for including IsSystemDeffined variables else false</param>
        /// <returns>List of DQA variables</returns>
        [HttpGet]
        [Route("variables/{strategyId}/{includeSystemDefined}")]
        public async Task<ActionResult<DQAVariableDto>> GetDQADLVariables(Guid strategyId, bool includeSystemDefined = false)
        {
            return Ok(await _dqaQueries.GetServiceLevelVariablesByStrategyAsync(strategyId, includeSystemDefined));
        }

        /// <summary>
        /// Gets data sources for DQAs
        /// </summary>
        /// <returns>List of data sources</returns>
        [HttpGet]
        [Route("datasources")]
        public async Task<ActionResult<DQADataSourcesDto>> GetDQADataSources()
        {
            return Ok(await _dqaQueries.GetDQADataSourcesAsync());
        }

        /// <summary>
        /// Creates service level data for DQA.
        /// </summary>
        /// <param name="command">Object of CreateServiceLevelDQACommand</param>
        /// <returns>Guid of service level record</returns>
        [HttpPost]
        [Route("servicelevel")]
        public async Task<ActionResult<Guid>> SaveServiceLevelDQA([FromBody] CreateServiceLevelDQACommand command)
        {
            command.CurrentUserId = GetCurrentUser().UserId;

            Guid serviceLevelId = await CommandAsync(command);

            return Created(HttpContext.Request.Path, serviceLevelId);
        }

        /// <summary>
        /// Updates service level data for DQA.
        /// </summary>
        /// <param name="command">Object of UpdateServiceLevelDQACommand</param>
        [HttpPut]
        [Route("servicelevel")]
        public async Task<ActionResult<Guid>> UpdateServiceLevelDQA([FromBody] UpdateServiceLevelDQACommand command)
        {
            command.CurrentUserId = GetCurrentUser().UserId;

            await CommandAsync(command);

            return NoContent();
        }

        /// <summary>
        /// Gets service level data
        /// </summary>
        /// <param name="assessmentId">Assessment id</param>
        /// <returns>Object of Service level for an assessment along with registers and variables</returns>
        [HttpGet]
        [Route("{assessmentId}/servicelevel")]
        public async Task<ActionResult<ServiceLevelDto>> GetServiceLevelDataAsync(Guid assessmentId)
        {
            return Ok(await _dqaQueries.GetServiceLevelForAssessmentAsync(assessmentId));
        }

        /// <summary>
        /// Export system-generated service level DQA excel
        /// </summary>
        /// <param name="serviceLevelId">Id of service Level </param>
        /// <returns>SL DQA Excel File Template</returns>
        [HttpGet]
        [Route("sl/export/{serviceLevelId}")]
        public async Task<FileContentResult> Export(Guid serviceLevelId)
        {
            try
            {
                string language = HttpContext?.Request?.Cookies[Constants.Common.I18next] ?? Constants.Common.DefaultLanguage;

                // todo: needs to follow async .. we cannot wait for file to be uploaded and processed and freeze the screens which will lead us in bad User experience
                FileResponseDto fileResponseDto = await _dqaQueries.ExportServiceLevelTemplate(serviceLevelId, base.GetCurrentUser().UserId);

                if (fileResponseDto?.FileData == null || fileResponseDto.FileData.Length == 0)
                {
                    throw new InvalidOperationException("Failed to generate service level template - no file data returned");
                }

                var result = new FileContentResult(fileResponseDto.FileData, Constants.DownloadDocument.ExcelFormat)
                {
                    FileDownloadName = fileResponseDto.FileName
                };

                return result;
            }
            catch (Exception ex)
            {
                // Log the error for debugging
                Console.WriteLine($"Error generating service level template for service level {serviceLevelId}: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                throw;
            }
        }

        /// <summary>
        /// Gets service level summary for an assessment.
        /// </summary>
        /// <param name="assessmentId">The id of the assessment for which service level is to be fetched</param>
        /// <returns>Object of service level summary including variable completeness and variable data</returns>
        [HttpGet]
        [Route("{assessmentId}/servicelevelsummary")]
        public async Task<ActionResult<ServiceLevelSummaryDto>> GetServiceLevelSummaryAsync(Guid assessmentId)
        {
            return Ok(await _dqaQueries.GetServiceLevelSummaryAsync(assessmentId));
        }

        /// <summary>
        /// Deletes service level data for DQA.
        /// </summary>
        /// <param name="command">Object of DeleteServiceLevelCommand</param>
        /// <returns>True if deletion of service level is successful</returns>
        [HttpDelete]
        [Route("servicelevel/delete")]
        public async Task<ActionResult<bool>> DeleteServiceLevel([FromBody] DeleteServiceLevelCommand command)
        {
            command.CurrentUserId = GetCurrentUser().UserId;

            return Ok(await CommandAsync(command));
        }

        /// <summary>
        /// Set service level status as finalized
        /// </summary>
        /// <param name="command">Object of FinalizeServiceLevelCommand containing properties required to finalize a service level</param>
        /// <returns>returns true if finalized successfully</returns>
        [HttpPut]
        [Route("serviceLevel/finalize")]
        public async Task<ActionResult<bool>> FinalizeServiceLevel([FromBody] FinalizeServiceLevelCommand command)
        {
            command.CurrentUserId = GetCurrentUser().UserId;

            return Ok(await CommandAsync(command));
        }

        /// <summary>
        /// Update the reasons for the observed data quality results for service level
        /// </summary>
        /// <param name="command">Object of UpdateObservedDataQualityResultReasonCommand contains properties required to update reasons for observed data quality results at a service level</param>
        /// <returns>True if the data quality reason is successfully updated</returns>
        [HttpPatch]
        [Route("serviceLevel/updateObservedDataQualityReason")]
        public async Task<ActionResult<bool>> UpdateObservedDataQualityResultReasonServiceLevelAsync([FromBody] UpdateObservedDataQualityResultReasonCommand command)
        {
            command.CurrentUserId = GetCurrentUser().UserId;

            return Ok(await CommandAsync(command));
        }

        /// <summary>
        /// Set the observed data quality results as finalised for the service level.
        /// </summary>
        /// <param name="command">Object of FinalizeObservedDataQualityResultReasonCommand containing properties required to finalize observed data quality results for service level</param>
        /// <returns>returns true if finalized successfully</returns>
        [HttpPatch]
        [Route("serviceLevel/finalizeObservedDataQualityReason")]
        public async Task<ActionResult<bool>> FinalizeObservedDataQualityResultReasonServiceLevelAsync([FromBody] FinalizeObservedDataQualityResultReasonCommand command)
        {
            command.CurrentUserId = GetCurrentUser().UserId;

            return Ok(await CommandAsync(command));
        }
    }
}
