﻿using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Collections.Generic;
using System.Threading.Tasks;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Dtos.InputDtos;
using WHO.MALARIA.Features.Helpers;
using WHO.MALARIA.Services.Handlers.Queries;

namespace WHO.MALARIA.Web.Apis
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class AnalyticalOutputController : BaseApiController
    {
        private readonly IAnalyticalOutputQueries _analyticalOutputQueries;
        private readonly IAssessmentQueries _assessmentQueries;
        private readonly ILogger<AnalyticalOutputController> _logger;

        public AnalyticalOutputController(
            IMediator mediator, IHttpContextAccessor httpContextAccessor,
            IAnalyticalOutputQueries analyticalOutputQueries, IAssessmentQueries assessmentQueries,
            ILogger<AnalyticalOutputController> logger) : base(mediator, httpContextAccessor)
        {
            _analyticalOutputQueries = analyticalOutputQueries;
            _assessmentQueries = assessmentQueries;
            _logger = logger;
        }

        /// <summary>
        /// Get collection of strategies, indicators, sub-objective, objective information that are associated with the analytical output and assessment 
        /// </summary>
        /// <param name="assessmentId">Assessment id</param>
        /// <returns>Collection of analytical output strategies, indicators, sub-objectives and objectives</returns>
        [HttpGet]
        [Route("details/{assessmentId}")]
        public async Task<ActionResult<AnalyticalOutputDetailDto>> GetAnalyticalOutputDetailsAsync(Guid assessmentId)
        {
            return Ok(await _analyticalOutputQueries.GetAnalyticalOutputDetailsAsync(assessmentId));
        }

        /// <summary>
        /// Generate analytical output report template
        /// </summary>
        /// <param name="request">object of AnalyticalOutputReportCommand </param>
        /// <returns>file of anlalytical output</returns>
        [HttpPost]
        [Route("report/download")]
        public async Task<FileContentResult> GenerateReportTemplateAsync([FromBody] AnalyticalOutputReportRequestModel request)
        {
            string fileNameToDwonload = string.Empty;
            try
            {
                request.CurrentUserId = base.GetCurrentUser().UserId;
                FileResponseDto fileResponseDto = await _analyticalOutputQueries.CreateAnalyticalOutputReportTemplateAsync(request);
                fileNameToDwonload = fileResponseDto.FileName;

                FileContentResult result = new(fileResponseDto.FileData, Constants.DownloadDocument.ExcelFormat)
                {
                    FileDownloadName = fileResponseDto.FileName
                };

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error Downloading Excel File: {fileNameToDwonload} for the User: {base.GetCurrentUser().UserId} : {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Generate analytical output report template for all subobjectives
        /// </summary>
        /// <param name="request">object of AnalyticalOutputReportCommand </param>
        /// <returns>file of anlalytical output</returns>
        [HttpPost]
        [Route("report/download/all")]
        public async Task<FileContentResult> GenerateReportTemplateAllAsync([FromBody] AnalyticalOutputReportRequestModel request)
        {
            request.CurrentUserId = base.GetCurrentUser().UserId;

            AnalyticalOutputDetailDto analyticalOutputDetailDto = await _analyticalOutputQueries.GetAnalyticalOutputDetailsAsync(request.AssessmentId);

            foreach (AnalyticalOutputIndicatorDto item in analyticalOutputDetailDto.Indicators.Where(x => x.StrategyId == request.StrategyId).ToList())
            {
                AnalyticalOutputIndicator analyticalOutputIndicator = new AnalyticalOutputIndicator()
                {
                    AssessmentIndicatorId = item.AssessmentIndicatorId,
                    AssessmentStrategyId = item.AssessmentStrategyId,
                    IndicatorId = item.Id
                };
                request.analyticalOutputIndicators.Add(analyticalOutputIndicator);
            }

            FileResponseDto fileResponseDto = await _analyticalOutputQueries.CreateAnalyticalOutputReportTemplateAsync(request);

            FileContentResult result = new FileContentResult(fileResponseDto.FileData, Constants.DownloadDocument.ExcelFormat)
            {
                FileDownloadName = fileResponseDto.FileName
            };

            return result;
        }

        /// <summary>
        /// Generate analytical output response for view
        /// </summary>
        /// <param name="request">object of AnalyticalOutputIndicatorResponseInputDto </param>       
        [HttpPost]
        [Route("indicator/response")]
        public async Task<IActionResult> GetAnalyticalOutputIndicatorResponseAsync([FromBody] AnalyticalOutputIndicatorResponseInputDto request)
        {
            request.CurrentUserId = base.GetCurrentUser().UserId;
            return Ok(await _analyticalOutputQueries.GetAnalyticalOutputIndicatorResponseAsync(request));
        }

        /// <summary>
        /// Get uploaded objective diagrams
        /// </summary>
        /// <param name="assessmentId">Assessment id for which the diagram has been uploaded</param>
        /// <param name="strategyId">Strategy id for which the diagram has been uploaded</param>        
        /// <returns>Diagrams and its other details</returns>
        [HttpGet]
        [Route("objective-diagram/{assessmentId}/{strategyId}")]
        public async Task<ActionResult> GetObjectiveDiagramsAsync(Guid assessmentId, Guid strategyId)
        {
            IEnumerable<FileDto> objectiveDiagrams = await _analyticalOutputQueries.GetObjectiveDiagramsAsync(assessmentId, strategyId);
            return Ok(objectiveDiagrams);
        }

        /// <summary>
        /// Download objective diagrams in .zip folder
        /// </summary>
        /// <param name="assessmentId">Assessment id for which the diagram has been uploaded</param>
        /// <param name="strategyId">Strategy id for which the diagram has been uploaded</param>        
        /// <returns>ZIP file along with the diagram files</returns>
        [HttpGet]
        [Route("objective-diagrams/{assessmentId}/{strategyId}/download")]
        public async Task<FileContentResult> GetObjectiveDiagramsDownloadAsync(Guid assessmentId, Guid strategyId)
        {
            IEnumerable<FileDto> fileDtos = await _analyticalOutputQueries.GetObjectiveDiagramsAsync(assessmentId, strategyId);

            byte[] memoryStream = UtilityHelper.CreateZIPFileForImages(fileDtos);

            return File(memoryStream, Constants.DownloadDocument.ContentType, Constants.DownloadDocument.ObjectiveDiagrams);
        }

        /// <summary>
        /// Download subobjective diagrams in .zip folder
        /// </summary>
        /// <param name="assessmentId">Assessment id for which the diagram has been uploaded</param>
        /// <param name="strategyId">Strategy id for which the diagram has been uploaded</param>        
        /// <returns>ZIP file along with the diagram files</returns>
        [HttpGet]
        [Route("subObjective-diagrams/{assessmentId}/{strategyId}/download")]
        public async Task<FileContentResult> GetSubObjectiveDiagramsDownloadAsync(Guid assessmentId, Guid strategyId)
        {
            IEnumerable<SubObjectiveDiagramDto> subObjectiveDiagrams = await _assessmentQueries.GetSubObjectiveDiagramsAsync(assessmentId, strategyId);

            List<FileDto> fileDtos = new List<FileDto>();

            foreach (SubObjectiveDiagramDto subObjectiveDiagram in subObjectiveDiagrams)
            {
                FileDto fileDto = new FileDto()
                {
                    FileName = subObjectiveDiagram.FileName,
                    Order = subObjectiveDiagram.Order,
                    Extension = subObjectiveDiagram.Extension,
                    File = subObjectiveDiagram.File
                };

                fileDtos.Add(fileDto);
            }
            fileDtos = fileDtos.OrderBy(o => o.Order).ToList();
            byte[] memoryStream = UtilityHelper.CreateZIPFileForImages(fileDtos);

            return File(memoryStream, Constants.DownloadDocument.ContentType, Constants.DownloadDocument.SubObjectiveDiagrams);
        }
    }
}
