﻿using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Linq.Expressions;
using System.Threading;
using System.Threading.Tasks;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Database;
using WHO.MALARIA.DocumentManager.Helpers;
using WHO.MALARIA.Domain.Commands.DQA.Desk_Level;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Dtos.InputDtos.DeskLevelDQA;
using WHO.MALARIA.Domain.Dtos.OutputDtos.DeskLevelDQA;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.Models.DQA.DeskLevel;
using WHO.MALARIA.Domain.SeedingMetadata;
using WHO.MALARIA.Features;
using WHO.MALARIA.Features.Extensions;
using WHO.MALARIA.Features.Helpers;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Services.Interfaces;
using WHO.MALARIA.Services.Rules.Assessment;
using WHO.MALARIA.Services.Rules.Shared;

namespace WHO.MALARIA.Services.Handlers.Commands.DQA.Desk_Level
{
    /// <summary>
    /// Handles DQA desk level file upload command
    /// </summary>
    public class ProcessDeskLevelFileCommandHandler : RuleBase, IRequestHandler<ProcessDeskLevelFileCommand, bool>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly DQAExcelSetting _dqaExcelSettings;
        private readonly IDQAReportGeneration _reportGeneration;
        private readonly ITranslationService _translationService;
        private readonly ICommonRuleChecker _commonRuleChecker;
        private readonly IAssessmentRuleChecker _assessmentRuleChecker;
        private readonly ICacheDataService _cacheService;

        public ProcessDeskLevelFileCommandHandler(
            IUnitOfWork unitOfWork,
            IOptions<DQAExcelSetting> dqaExcelSettings,
            IDQAReportGeneration reportGeneration,
            ITranslationService translationService,
            ICommonRuleChecker commonRuleChecker,
            IAssessmentRuleChecker assesmentRuleChecker,
            ICacheDataService cacheService)
        {
            _unitOfWork = unitOfWork;
            _dqaExcelSettings = dqaExcelSettings.Value;
            _reportGeneration = reportGeneration;
            _translationService = translationService;
            _commonRuleChecker = commonRuleChecker;
            _assessmentRuleChecker = assesmentRuleChecker;
            _cacheService = cacheService;
        }

        /// <summary>
        /// Processes the desk level dqa excel data sheet and uploads data in database
        /// </summary>
        /// <param name="request">Command includes properties related to desk level file processing</param>
        /// <param name="cancellationToken">Notify the cancellation request</param>
        /// <returns>Returns true if file data is saved in database</returns>
        public async Task<bool> Handle(ProcessDeskLevelFileCommand request, CancellationToken cancellationToken)
        {
            // Check Business Rules
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, request.AssessmentId, "AssessmentId"));
            CheckRule(new AssessmentShouldExistRule(_translationService, _assessmentRuleChecker, request.AssessmentId));
            CheckRule(new UserShouldHaveOperationPermissionOnAssessmentRule(_translationService, _assessmentRuleChecker, request.AssessmentId, request.CurrentUserId, UserAssessmentPermission.CanGenerateOrUploadDeskLevelDQATemplate));

            CheckRule(new IsFileNullCheckRule(_translationService, request.File, Constants.Exception.SelectFile));

            string fileExtension = Path.GetExtension(request.File.FileName);

            CheckRule(new IsFileExtensionValidRule(_translationService, fileExtension, Constants.Common.ValidExcelExtensions));

            string sheetName1, sheetName2;
            List<DQAVariableDto> excelColumns = await GetDQAExcelSheetColumnsNameAsync();

            if (request.LanguageId == Constants.Common.DefaultLanguage)
            {
                //english
                sheetName1 =_dqaExcelSettings.DeskLevel.Sheet1Name;
                sheetName2 =_dqaExcelSettings.DeskLevel.Sheet2Name;
            }
            else
            {
                // french
                sheetName1 =_dqaExcelSettings.DeskLevel.Sheet1Name_FR;
                sheetName2 =_dqaExcelSettings.DeskLevel.Sheet2Name_FR;
                excelColumns.ForEach(x => x.Name= x.Name_FR);
            }

         

            List<TemplateDataSourceCombinedVariablesDto> dataSystem1 = GetDataFromExcelsheet(request.File, sheetName1,
                                                                        _dqaExcelSettings.DeskLevel.SkipExcelRows, _dqaExcelSettings.DeskLevel.ExcelHeaderRowStartsFrom, true, excelColumns);

            List<TemplateDataSourceCombinedVariablesDto> dataSystem2 = GetDataFromExcelsheet(request.File, sheetName2,
                                                                        _dqaExcelSettings.DeskLevel.SkipExcelRows, _dqaExcelSettings.DeskLevel.ExcelHeaderRowStartsFrom, true, excelColumns);

            List<DeskLevelDataSystem1> dataSystem1HealthFacilities = GetDataSystem1Sheet(dataSystem1, request.AssessmentId, request.CurrentUserId);

            List<DeskLevelDataSystem2> dataSystem2HealthFacilities = GetDataSystem2Sheet(dataSystem2, request.AssessmentId, request.CurrentUserId);

            // Check which data systems are configured for this assessment
            DeskLevel deskLevelData = await _unitOfWork.DeskLevelRepository.GetAsync(x => x.AssessmentId == request.AssessmentId);
            IEnumerable<DataSource> configuredDataSources = await _unitOfWork.DataSourceRepository.GetListAsync(x => x.DeskLevelId == deskLevelData.Id);
            IEnumerable<DQADataSource> dqaDataSources = await _unitOfWork.DQADataSourceRepository.GetListAsync(x => configuredDataSources.Select(p => p.DataSourceId).Contains(x.Id));

            bool isDataSystem1Configured = dqaDataSources.Any(x => x.DataSystem == (byte)DQADataSystem.System1);
            bool isDataSystem2Configured = dqaDataSources.Any(x => x.DataSystem == (byte)DQADataSystem.System2);

            // Validate that we have data for the configured data systems
            bool hasRequiredData = false;
            if (isDataSystem1Configured && isDataSystem2Configured)
            {
                // Both systems configured - require data for both
                hasRequiredData = dataSystem1HealthFacilities.Any() && dataSystem2HealthFacilities.Any();
            }
            else if (isDataSystem1Configured)
            {
                // Only data system 1 configured - require data for system 1 only
                hasRequiredData = dataSystem1HealthFacilities.Any();
            }
            else if (isDataSystem2Configured)
            {
                // Only data system 2 configured - require data for system 2 only
                hasRequiredData = dataSystem2HealthFacilities.Any();
            }

            if (hasRequiredData)
            {
                // Set data into cache memory - only for configured systems with data
                if (isDataSystem1Configured && isDataSystem2Configured && dataSystem1HealthFacilities.Any() && dataSystem2HealthFacilities.Any())
                {
                    SetDataSystemDataIntoCache(dataSystem1HealthFacilities, dataSystem2HealthFacilities, request.AssessmentId);
                    await SetDiscrepancyForSameVariablesWithConcordanceDataIntoCache(dataSystem1, dataSystem2, request.AssessmentId);
                }

                // Always set consistency discrepancy for data system 1 if it has data
                if (isDataSystem1Configured && dataSystem1HealthFacilities.Any())
                {
                    await SetDiscrepancyForConsistancyBetweenVariablesDataIntoCache(dataSystem1, request.AssessmentId);
                }

                // Save data for configured systems
                if (isDataSystem1Configured && dataSystem1HealthFacilities.Any())
                {
                    await SaveDataSystem1Sheet(dataSystem1, request.AssessmentId, request.CurrentUserId, cancellationToken);
                }

                if (isDataSystem2Configured && dataSystem2HealthFacilities.Any())
                {
                    await SaveDataSystem2Sheet(dataSystem2, request.AssessmentId, request.CurrentUserId, cancellationToken);
                }

                // Save national level summary data
                await SaveNationalLevelSummaryData(request.AssessmentId, dataSystem1, dataSystem2, cancellationToken);

                //save file name
                await SaveDesklevelFileName($"{request.File.FileName} ({CommandGenerator.CalcuateFileSize(request.File.Length)})", request.AssessmentId, request.CurrentUserId, cancellationToken);

            }

            return true;
        }

        #region Save Excel Sheet Data

        /// <summary>
        /// Method to Get excel main HF sheet data
        /// </summary>
        /// <param name="sheetData">List of instance of TemplateDataSourceCombinedVariablesDto</param>
        /// <param name="assessmentId">Guid</param>
        private List<DeskLevelDataSystem1> GetDataSystem1Sheet(List<TemplateDataSourceCombinedVariablesDto> sheetData, Guid assessmentId, Guid createdBy)
        {
            List<DeskLevelDataSystem1> entityData = UtilityHelper.GetEntityDataFromModel<DeskLevelDataSystem1, TemplateDataSourceCombinedVariablesDto>(sheetData, assessmentId, createdBy);

            return entityData;
        }

        /// <summary>
        /// Method to get excel concordance HF sheet data
        /// </summary>
        /// <param name="sheetData">List of instance of TemplateDataSourceCombinedVariablesDto</param>
        /// <param name="assessmentId">Guid</param>
        private List<DeskLevelDataSystem2> GetDataSystem2Sheet(List<TemplateDataSourceCombinedVariablesDto> sheetData, Guid assessmentId, Guid createdBy)
        {
            List<DeskLevelDataSystem2> entityData = UtilityHelper.GetEntityDataFromModel<DeskLevelDataSystem2, TemplateDataSourceCombinedVariablesDto>(sheetData, assessmentId, createdBy);

            return entityData;
        }

        /// <summary>
        /// Method to save excel main HF sheet data into database
        /// </summary>
        /// <param name="sheetData">List of instance of TemplateDataSourceCombinedVariablesDto</param>
        /// <param name="assessmentId">Guid</param>
        private async Task SaveDataSystem1Sheet(List<TemplateDataSourceCombinedVariablesDto> sheetData, Guid assessmentId, Guid createdBy, CancellationToken cancellationToken)
        {
            List<DeskLevelDataSystem1> entityData = new List<DeskLevelDataSystem1>();

            IEnumerable<DeskLevelDataSystem1> deskLevelDataSystem1Records = await _unitOfWork.DeskLevelDataSystem1Repository.GetListAsync(x => x.AssessmentId == assessmentId);

            if (deskLevelDataSystem1Records.Any())
            {
                _unitOfWork.DeskLevelDataSystem1Repository.RemoveRange(deskLevelDataSystem1Records);

                if (await _unitOfWork.CommitAsync(cancellationToken) == 0)
                {
                    throw new ApplicationException();
                }
            }

            DataTable dataSystemTable = sheetData.ToDataTable();

            DataColumn assessmentIdColumn = new DataColumn
            {
                ColumnName = "AssessmentId",
                DataType = typeof(Guid)
            };

            if (!dataSystemTable.Columns.Contains("AssessmentId"))
            {
                dataSystemTable.Columns.Add(assessmentIdColumn);
            }

            dataSystemTable.AsEnumerable().ToList().ForEach(D => D.SetField("AssessmentId", assessmentId));
            dataSystemTable.AsEnumerable().ToList().ForEach(D => D.SetField("CreatedBy", createdBy));

            dataSystemTable.TableName = "DataSystem1";
            string dataSystemRecordsInXml = ConvertDatatableToXML(dataSystemTable);

            await _unitOfWork.DeskLevelDataSystem1Repository.InsertDataSystem1Records(dataSystemRecordsInXml);

        }

        /// <summary>
        /// Method to save excel concordance HF sheet data into database
        /// </summary>
        /// <param name="sheetData">List of instance of TemplateDataSourceCombinedVariablesDto</param>
        /// <param name="assessmentId">Guid</param>
        private async Task SaveDataSystem2Sheet(List<TemplateDataSourceCombinedVariablesDto> sheetData, Guid assessmentId, Guid createdBy, CancellationToken cancellationToken)
        {
            List<DeskLevelDataSystem2> entityData = new List<DeskLevelDataSystem2>();

            IEnumerable<DeskLevelDataSystem2> deskLevelDataSystem2Records = await _unitOfWork.DeskLevelDataSystem2Repository.GetListAsync(x => x.AssessmentId == assessmentId);

            if (deskLevelDataSystem2Records.Any())
            {
                _unitOfWork.DeskLevelDataSystem2Repository.RemoveRange(deskLevelDataSystem2Records);

                if (await _unitOfWork.CommitAsync(cancellationToken) == 0)
                {
                    throw new ApplicationException();
                }
            }

            DataTable dataSystemTable = sheetData.ToDataTable();

            DataColumn assessmentIdColumn = new DataColumn
            {
                ColumnName = "AssessmentId",
                DataType = typeof(Guid)
            };

            if (!dataSystemTable.Columns.Contains("AssessmentId"))
            {
                dataSystemTable.Columns.Add(assessmentIdColumn);
            }

            dataSystemTable.AsEnumerable().ToList().ForEach(D => D.SetField("AssessmentId", assessmentId));
            dataSystemTable.AsEnumerable().ToList().ForEach(D => D.SetField("CreatedBy", createdBy));

            dataSystemTable.TableName = "DataSystem2";
            string dataSystemRecordsInXml = ConvertDatatableToXML(dataSystemTable);

            await _unitOfWork.DeskLevelDataSystem2Repository.InsertDataSystem2Records(dataSystemRecordsInXml);
        }

        /// <summary>
        /// Saves national Level Summary Data
        /// </summary>
        /// <param name="assessmentId">Assessment Id</param>
        /// <param name="dataSystem1">List of instance of TemplateDataSourceCombinedVariablesDto</param>
        /// <param name="dataSystem2">List of instance of TemplateDataSourceCombinedVariablesDto</param>
        private async Task SaveNationalLevelSummaryData(Guid assessmentId, List<TemplateDataSourceCombinedVariablesDto> dataSystem1, List<TemplateDataSourceCombinedVariablesDto> dataSystem2, CancellationToken cancellationToken)
        {
            List<short> years = dataSystem1.Select(x => (short)x.Year).Distinct().OrderBy(x => x).ToList();

            Dictionary<string, int> indicators = UtilityHelper.GetEnumerableData<DQAIndicatorReport>();
            Dictionary<string, int> reportTypes = UtilityHelper.GetEnumerableData<DQAReportType>();
            Dictionary<string, int> consistencyOverTimeIndicators = UtilityHelper.GetEnumerableData<ConsistencyOverTimeKeyIndicator>();

            List<Summary> summaries = new List<Summary>();

            //  Get Selected variable dto
            SelectedVariableDto selectedVariables = await _unitOfWork.DQARepository.GetDeskLevelSelectedVariablesForCalculation(assessmentId);

            years.ForEach((short year) =>
            {
                Summary summary = new Summary();

                List<TemplateDataSourceCombinedVariablesDto> filteredDataSystem1 = new List<TemplateDataSourceCombinedVariablesDto>();
                List<TemplateDataSourceCombinedVariablesDto> filteredDataSystem2 = new List<TemplateDataSourceCombinedVariablesDto>();

                summary.Year = year;
                summary.AssessmentId = assessmentId;
                summary.Type = (byte)DQADLSummaryResultType.NationalLevelResults;

                filteredDataSystem1 = dataSystem1.Where(x => (short)x.Year == year).ToList();
                filteredDataSystem2 = dataSystem2.Where(x => (short)x.Year == year).ToList();

                foreach (KeyValuePair<string, int> indicator in indicators)
                {
                    string indicatorTitle = string.Empty;

                    List<DQAReportDto> calculatedReports = new List<DQAReportDto>();

                    switch (indicator.Value)
                    {
                        case (int)DQAIndicatorReport.ReportingCompleteness:

                            foreach (KeyValuePair<string, int> reportType in reportTypes)
                            {
                                switch (reportType.Value)
                                {
                                    case (int)DQAReportType.NationalReport:

                                        calculatedReports = _reportGeneration.GenerateReport(indicator.Value, reportType.Value, filteredDataSystem1, filteredDataSystem2, assessmentId, selectedVariables);
                                        double percentage = calculatedReports.Count > 0 ? calculatedReports.FirstOrDefault().PercentageValue : 0;

                                        summary.ReportCompleteness = percentage;

                                        break;
                                }
                            }

                            break;

                        case (int)DQAIndicatorReport.ReportingTimeliness:

                            foreach (KeyValuePair<string, int> reportType in reportTypes)
                            {
                                switch (reportType.Value)
                                {
                                    case (int)DQAReportType.NationalReport:

                                        calculatedReports = _reportGeneration.GenerateReport(indicator.Value, reportType.Value, filteredDataSystem1, filteredDataSystem2, assessmentId, selectedVariables);
                                        double percentage = calculatedReports.Count > 0 ? calculatedReports.FirstOrDefault().PercentageValue : 0;

                                        summary.ReportTimeliness = percentage;

                                        break;
                                }
                            }

                            break;

                        case (int)DQAIndicatorReport.ReportingVariableCompleteness:

                            foreach (KeyValuePair<string, int> reportType in reportTypes)
                            {
                                switch (reportType.Value)
                                {
                                    case (int)DQAReportType.NationalReport3:

                                        calculatedReports = _reportGeneration.GenerateReport(indicator.Value, reportType.Value, filteredDataSystem1, filteredDataSystem2, assessmentId, selectedVariables);
                                        double percentage = calculatedReports.Count > 0 ? calculatedReports.FirstOrDefault().PercentageValue : 0;

                                        summary.VariableCompleteness = percentage;

                                        break;
                                }
                            }

                            break;

                        case (int)DQAIndicatorReport.ReportingConsistencyBtwVariables:

                            foreach (KeyValuePair<string, int> reportType in reportTypes)
                            {
                                switch (reportType.Value)
                                {
                                    case (int)DQAReportType.NationalReport3:

                                        calculatedReports = _reportGeneration.GenerateReport(indicator.Value, reportType.Value, filteredDataSystem1, filteredDataSystem2, assessmentId, selectedVariables);
                                        double percentage = calculatedReports.Count > 0 ? calculatedReports.FirstOrDefault().PercentageValue : 0;

                                        summary.VariableConsistency = percentage;

                                        break;
                                }
                            }

                            break;

                        case (int)DQAIndicatorReport.ReportingConcordance:

                            foreach (KeyValuePair<string, int> reportType in reportTypes)
                            {
                                switch (reportType.Value)
                                {
                                    case (int)DQAReportType.NationalReport3:

                                        calculatedReports = _reportGeneration.GenerateReport(indicator.Value, reportType.Value, filteredDataSystem1, filteredDataSystem2, assessmentId, selectedVariables);
                                        double percentage = calculatedReports.Count > 0 ? calculatedReports.FirstOrDefault().PercentageValue : 0;

                                        summary.VariableConcordance = percentage;

                                        break;
                                }
                            }

                            break;
                    }
                }

                summaries.Add(summary);
            });

            IEnumerable<Summary> existingSummaries = await _unitOfWork.SummaryRepository.GetListAsync(x => x.AssessmentId == assessmentId);

            if (existingSummaries.Any())
            {
                _unitOfWork.SummaryRepository.RemoveRange(existingSummaries);
            }

            _unitOfWork.SummaryRepository.AddRange(summaries);

            if (await _unitOfWork.CommitAsync(cancellationToken) == 0)
            {
                throw new ApplicationException();
            }
        }

        /// <summary>
        /// Method to save excel file name into database
        /// </summary>
        /// <param name="sheetData">List of instance of TemplateDataSourceCombinedVariablesDto</param>
        /// <param name="assessmentId">Guid</param>
        private async Task SaveDesklevelFileName(string name, Guid assessmentId, Guid updatedBy, CancellationToken cancellationToken)
        {

            DeskLevel deskLevel = await _unitOfWork.DeskLevelRepository.GetAsync(x => x.AssessmentId == assessmentId);

            if (deskLevel!= null)
            {
                deskLevel.FileName = name;
                _unitOfWork.DeskLevelRepository.Update(deskLevel);

                if (await _unitOfWork.CommitAsync(cancellationToken) == 0)
                {
                    throw new ApplicationException();
                }
            }

        }
        #endregion

        #region Private Method
        /// <summary>
        /// Method to get data from excel sheet based on below parameters
        /// </summary>
        /// <param name="excelFilePath">full file path for excel template</param>
        /// <param name="sheetName">name of sheet</param>
        /// <param name="skipExcelRows">number of rows to be skipped</param>
        /// <param name="headerColumnStartsFrom">row index from header starts</param>
        /// <param name="matchByModelPropertyValue">flag to decide whether model needs to be match by property name or value</param>
        /// <param name="cacheKeyName">key name by which data is going to be saved in cache</param>
        /// <returns>list of data in TemplateDataSourceCombinedVariables model </returns>
        private List<TemplateDataSourceCombinedVariablesDto> GetDataFromExcelsheet(IFormFile file, string sheetName,
            int skipExcelRows, int headerColumnStartsFrom, bool matchByModelPropertyValue, List<DQAVariableDto> excelColumns)
        {
            var dataFromExcelSheet = ExcelOperations.ReadExcel<ExcelTemplateHeaderListDto>(file, sheetName, skipExcelRows, headerColumnStartsFrom, matchByModelPropertyValue, excelColumns);

            var excelTemplateData = new List<TemplateDataSourceCombinedVariablesDto>();

            dataFromExcelSheet.ForEach((ExcelTemplateHeaderListDto item) =>
            {
                TemplateDataSourceCombinedVariablesDto templateData = new TemplateDataSourceCombinedVariablesDto
                {
                    Province = item.Province,
                    District = item.District,
                    HealthFacilityName = item.HealthFacilityName,
                    HealthFacilityType = item.HealthFacility,
                    Year = !string.IsNullOrEmpty(item.Year) ? Convert.ToInt32(item.Year) : 0,
                    Month = !string.IsNullOrEmpty(item.Month) ? Convert.ToInt32(item.Month) : 0,
                    IsReportsOnTime = UtilityHelper.ConvertStringToBoolean(item.IsReportsOnTime),
                    IsReportsReceived = UtilityHelper.ConvertStringToBoolean(item.IsReportsReceived),
                    IsExpectedReports = UtilityHelper.ConvertStringToBoolean(item.IsExpectedReports),
                    TotalMalariaCases = UtilityHelper.ConvertStringToInt(item.TotalMalariaCases),
                    ConfirmMalariaCases = UtilityHelper.ConvertStringToInt(item.ConfirmMalariaCases),
                    MicroscopyTested = UtilityHelper.ConvertStringToInt(item.MicroscopyTested),
                    RDTTested = UtilityHelper.ConvertStringToInt(item.RDTTested),
                    MicroscopyPositive = UtilityHelper.ConvertStringToInt(item.MicroscopyPositive),
                    RDTPositive = UtilityHelper.ConvertStringToInt(item.RDTPositive),
                    AllCauseOutpatients = UtilityHelper.ConvertStringToInt(item.AllCauseOutpatients),
                    AllCauseInpatients = UtilityHelper.ConvertStringToInt(item.AllCauseInpatients),
                    AllCauseDeaths = UtilityHelper.ConvertStringToInt(item.AllCauseDeaths),
                    MalariaInpatients = UtilityHelper.ConvertStringToInt(item.MalariaInpatients),
                    MalariaInpatientDeaths = UtilityHelper.ConvertStringToInt(item.MalariaInpatientDeaths),
                    ConfirmedMalariaCasesTreated = UtilityHelper.ConvertStringToInt(item.ConfirmedMalariaCasesTreated),
                    SuspectedMalariaCases = UtilityHelper.ConvertStringToInt(item.SuspectedMalariaCases),
                    PresumedMalariaCases = UtilityHelper.ConvertStringToInt(item.PresumedMalariaCases),
                    IPTp = UtilityHelper.ConvertStringToInt(item.IPTp),
                    ANC = UtilityHelper.ConvertStringToInt(item.ANC)
                };

                excelTemplateData.Add(templateData);
            });

            return excelTemplateData;
        }

        /// <summary>
        /// Method to generate Excel sheet columns name 
        /// </summary>
        /// <returns></returns>
        private async Task<List<DQAVariableDto>> GetDQAExcelSheetColumnsNameAsync()
        {
            // varibale columns name
            IEnumerable<DQAVariableDto> dQAVariableDtos = await _unitOfWork.DQARepository.GetDQAVariablesAsync();

            // add basic column name
            List<DQAVariableDto> allColumns = dQAVariableDtos.ToList();

            allColumns.AddRange(DQAConstants.BasicColumns);

            return allColumns;

        }

        /// <summary>
        /// Method to find difference between 2 variables
        /// </summary>
        /// <param name="value1">An int value</param>
        /// <param name="value2">An int value</param>
        /// <returns>Difference in int</returns>
        private int FindDifference(int? value1, int? value2)
        {
            if (value1 != null || value2 != null)
            {
                if (value1 > 0 && value2 > 0)
                {
                    return (value1 ?? 0) - (value2 ?? 0);
                }
            }
            return 0;
        }

        /// <summary>
        /// Method to set discrapancy data to check consistancy between variables into cache memory
        /// </summary>
        /// <param name="mainSheetData">Data from main sheet, an instance of list of TemplateDataSourceCombinedVariablesDto</param>
        public async Task SetDiscrepancyForConsistancyBetweenVariablesDataIntoCache(List<TemplateDataSourceCombinedVariablesDto> mainSheetData, Guid assessmentId)
        {
            var modifiedDataSource = new List<TemplateDataSourceCombinedVariablesDto>();

            mainSheetData.ForEach((TemplateDataSourceCombinedVariablesDto data) =>
            {
                var consistencyCalc = new TemplateDataSourceCombinedVariablesDto();
                consistencyCalc.Province = data.Province;
                consistencyCalc.District = data.District;
                consistencyCalc.HealthFacilityName = data.HealthFacilityName;
                consistencyCalc.HealthFacilityType = data.HealthFacilityType;
                consistencyCalc.Year = data.Year;
                consistencyCalc.Month = data.Month;
                consistencyCalc.IsReportsOnTime = data.IsReportsOnTime;
                consistencyCalc.IsReportsReceived = data.IsReportsReceived;
                consistencyCalc.IsExpectedReports = data.IsExpectedReports;

                consistencyCalc.TotalMalariaCases = data.TotalMalariaCases;
                consistencyCalc.ConfirmMalariaCases = data.ConfirmMalariaCases;
                consistencyCalc.MicroscopyTested = data.MicroscopyTested;
                consistencyCalc.RDTTested = data.RDTTested;
                consistencyCalc.MicroscopyPositive = data.MicroscopyPositive;
                consistencyCalc.RDTPositive = data.RDTPositive;
                consistencyCalc.AllCauseOutpatients = data.AllCauseOutpatients;
                consistencyCalc.AllCauseInpatients = data.AllCauseInpatients;
                consistencyCalc.AllCauseDeaths = data.AllCauseDeaths;
                consistencyCalc.MalariaInpatients = data.MalariaInpatients;
                consistencyCalc.MalariaInpatientDeaths = data.MalariaInpatientDeaths;
                consistencyCalc.ConfirmedMalariaCasesTreated = data.ConfirmedMalariaCasesTreated;

                consistencyCalc.SuspectedMalariaCases = data.SuspectedMalariaCases;
                consistencyCalc.PresumedMalariaCases = data.PresumedMalariaCases;
                consistencyCalc.IPTp = data.IPTp;
                consistencyCalc.ANC = data.ANC;
                modifiedDataSource.Add(consistencyCalc);
            });

            await Task.Run(() => _cacheService.SetDataIntoCache(DQAConstants.DiscrepancyForConsistancyBetweenVariables+"_"+assessmentId.ToString(), modifiedDataSource));
        }

        /// <summary>
        /// Method to set discrepancy data for variables with concordance data into cache
        /// </summary>
        /// <param name="mainSheetData">Data from main sheet, an instance of list of TemplateDataSourceCombinedVariablesDto</param>
        /// <param name="concordanceSheetData">Data from concordance sheet, an instance of TemplateDataSourceCombinedVariablesDto</param>
        public async Task SetDiscrepancyForSameVariablesWithConcordanceDataIntoCache(List<TemplateDataSourceCombinedVariablesDto> mainSheetData, List<TemplateDataSourceCombinedVariablesDto> concordanceSheetData, Guid assessmentId)
        {
            var modifiedDataSource = new List<TemplateDataSourceCombinedVariablesDto>();

            mainSheetData.ForEach((TemplateDataSourceCombinedVariablesDto data) =>
            {
                Expression<Func<TemplateDataSourceCombinedVariablesDto, bool>> predicate = GetMainAndConcordanceDataDifferencePredicate(data);

                var concordanceDiscrepancy = new TemplateDataSourceCombinedVariablesDto();

                concordanceDiscrepancy.Province = data.Province;
                concordanceDiscrepancy.District = data.District;
                concordanceDiscrepancy.HealthFacilityName = data.HealthFacilityName;
                concordanceDiscrepancy.HealthFacilityType = data.HealthFacilityType;
                concordanceDiscrepancy.Year = data.Year;
                concordanceDiscrepancy.Month = data.Month;
                concordanceDiscrepancy.IsReportsOnTime = data.IsReportsOnTime;
                concordanceDiscrepancy.IsExpectedReports = data.IsExpectedReports;

                bool concordanceReportReceived = concordanceSheetData.FirstOrDefault(predicate.Compile())?.IsReportsReceived ?? false;

                if (data.IsReportsReceived && concordanceReportReceived)
                {
                    concordanceDiscrepancy.IsReportsReceived = data.IsReportsReceived;
                    concordanceDiscrepancy.TotalMalariaCases = FindDifference(data.TotalMalariaCases, concordanceSheetData.FirstOrDefault(predicate.Compile())?.TotalMalariaCases);
                    concordanceDiscrepancy.ConfirmMalariaCases = FindDifference(data.ConfirmMalariaCases, concordanceSheetData.FirstOrDefault(predicate.Compile())?.ConfirmMalariaCases);
                    concordanceDiscrepancy.MicroscopyTested = FindDifference(data.MicroscopyTested, concordanceSheetData.FirstOrDefault(predicate.Compile())?.MicroscopyTested);
                    concordanceDiscrepancy.RDTTested = FindDifference(data.RDTTested, concordanceSheetData.FirstOrDefault(predicate.Compile())?.RDTTested);
                    concordanceDiscrepancy.MicroscopyPositive = FindDifference(data.MicroscopyPositive, concordanceSheetData.FirstOrDefault(predicate.Compile())?.MicroscopyPositive);
                    concordanceDiscrepancy.RDTPositive = FindDifference(data.RDTPositive, concordanceSheetData.FirstOrDefault(predicate.Compile())?.RDTPositive);
                    concordanceDiscrepancy.AllCauseOutpatients = FindDifference(data.AllCauseOutpatients, concordanceSheetData.FirstOrDefault(predicate.Compile())?.AllCauseOutpatients);
                    concordanceDiscrepancy.AllCauseInpatients = FindDifference(data.AllCauseInpatients, concordanceSheetData.FirstOrDefault(predicate.Compile())?.AllCauseInpatients);
                    concordanceDiscrepancy.AllCauseDeaths = FindDifference(data.AllCauseDeaths, concordanceSheetData.FirstOrDefault(predicate.Compile())?.AllCauseDeaths);
                    concordanceDiscrepancy.MalariaInpatients = FindDifference(data.MalariaInpatients, concordanceSheetData.FirstOrDefault(predicate.Compile())?.MalariaInpatients);
                    concordanceDiscrepancy.MalariaInpatientDeaths = FindDifference(data.MalariaInpatientDeaths, concordanceSheetData.FirstOrDefault(predicate.Compile())?.MalariaInpatientDeaths);
                    concordanceDiscrepancy.ConfirmedMalariaCasesTreated = FindDifference(data.ConfirmedMalariaCasesTreated, concordanceSheetData.FirstOrDefault(predicate.Compile())?.ConfirmedMalariaCasesTreated);

                    concordanceDiscrepancy.SuspectedMalariaCases = FindDifference(data.SuspectedMalariaCases, concordanceSheetData.FirstOrDefault(predicate.Compile())?.SuspectedMalariaCases);
                    concordanceDiscrepancy.PresumedMalariaCases = FindDifference(data.PresumedMalariaCases, concordanceSheetData.FirstOrDefault(predicate.Compile())?.PresumedMalariaCases);
                    concordanceDiscrepancy.IPTp = FindDifference(data.IPTp, concordanceSheetData.FirstOrDefault(predicate.Compile())?.IPTp);
                    concordanceDiscrepancy.ANC = FindDifference(data.ANC, concordanceSheetData.FirstOrDefault(predicate.Compile())?.ANC);
                }

                modifiedDataSource.Add(concordanceDiscrepancy);
            });

            await Task.Run(() => _cacheService.SetDataIntoCache(DQAConstants.DiscrepancyForSameVariablesWithConcordance+"_"+assessmentId.ToString(), modifiedDataSource));
        }

        /// <summary>
        /// Method to set Data systems data into Cache
        /// </summary>
        /// <param name="dataSystem1">List of instance of DeskLevelDataSystem1</param>
        /// <param name="dataSystem2">List of instance of DeskLevelDataSystem2</param>
        /// <param name="assessmentId">Guid</param>
        public void SetDataSystemDataIntoCache(List<DeskLevelDataSystem1> dataSystem1, List<DeskLevelDataSystem2> dataSystem2, Guid assessmentId)
        {
            _cacheService.RemoveDataFromCache(DQAConstants.DeskLevelDataSystem1 +"_" +assessmentId.ToString());
            _cacheService.RemoveDataFromCache(DQAConstants.DeskLevelDataSystem2 +"_" +assessmentId.ToString());
            _cacheService.SetDataIntoCache(DQAConstants.DeskLevelDataSystem1 +"_" +assessmentId.ToString(), dataSystem1);
            _cacheService.SetDataIntoCache(DQAConstants.DeskLevelDataSystem2 +"_" +assessmentId.ToString(), dataSystem2);
        }

        /// <summary>
        /// Generates predicate for the input class
        /// </summary>
        /// <param name="requestVariables">An instance of TemplateDataSourceCombinedVariablesDto</param>
        /// <returns>Report object as response</returns>
        private Expression<Func<TemplateDataSourceCombinedVariablesDto, bool>> GetMainAndConcordanceDataDifferencePredicate(TemplateDataSourceCombinedVariablesDto requestVariables)
        {
            var predicate = PredicateExtensions.Begin<TemplateDataSourceCombinedVariablesDto>();

            if (requestVariables.Year > 0)
            {
                predicate = predicate.And(x => x.Year == requestVariables.Year);
            }

            if (!string.IsNullOrEmpty(requestVariables.Province))
            {
                predicate = predicate.And(x => x.Province == requestVariables.Province);
            }

            if (!string.IsNullOrEmpty(requestVariables.District))
            {
                predicate = predicate.And(x => x.District == requestVariables.District);
            }

            if (!string.IsNullOrEmpty(requestVariables.HealthFacilityName))
            {
                predicate = predicate.And(x => x.HealthFacilityName == requestVariables.HealthFacilityName);
            }

            if (!string.IsNullOrEmpty(requestVariables.HealthFacilityType))
            {
                predicate = predicate.And(x => x.HealthFacilityType == requestVariables.HealthFacilityType);
            }

            if (requestVariables.Month > 0)
            {
                predicate = predicate.And(x => x.Month == requestVariables.Month);
            }

            if (requestVariables.IsReportsOnTime)
            {
                predicate = predicate.And(x => x.IsReportsOnTime == requestVariables.IsReportsOnTime);
            }

            if (requestVariables.IsReportsReceived)
            {
                predicate = predicate.And(x => x.IsReportsReceived == requestVariables.IsReportsReceived);
            }

            if (requestVariables.IsExpectedReports)
            {
                predicate = predicate.And(x => x.IsExpectedReports == requestVariables.IsExpectedReports);
            }

            return predicate;
        }

        /// <summary>
        /// Method we can convert datatable to xml
        /// </summary>
        /// <param name="table">Instance of DataTable</param>
        /// <returns>Xml String of input</returns>
        public string ConvertDatatableToXML(DataTable table)
        {
            using (MemoryStream memoryStream = new MemoryStream())
            {
                table.WriteXml(memoryStream, true);
                memoryStream.Seek(0, SeekOrigin.Begin);
                StreamReader streamReader = new StreamReader(memoryStream);
                string xmlstring = streamReader.ReadToEnd();
                return (xmlstring);
            }
        }

        #endregion
    }
}
